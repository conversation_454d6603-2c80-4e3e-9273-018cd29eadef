## 封装脚手架工具

- 原因
  - 公司涉及到的项目种类过于多(Vue3 Vue2 H5 各个移动端平台),无法统一管理,无法规范化
  - 每次新建项目需要配置规范(<PERSON><PERSON><PERSON> Prettier cspell <PERSON><PERSON>),安装依赖等各种操作(ElementPlus Vant 集成 sdk 等)
  - 无法统一部署发布
  - 大量的重复 cv 操作
- 选型
  - pnpm + workspace + monorepo 管理方式
  - 统一配置,可以单独发布子包,不耦合
  - 配合 TurboPack 打包缓存增加构建速度,配合 tsup 打包 ts 零 配置实现打包子包
- 设计架构
  - 插件化思想设置各个命令
  - 统一集成在 packages/cli目录下 配合 bin lib dist 实现
  - 命令行参数解析工具 commander
  - 命令行交互工具 prompts
  - 命令行打印美化工具 consola picocolors
  - 远程仓库拉去模板工具 giget
  - 命令行loading 效果工具 ora
  - 文件操作增强操作 fs-extra
  - 打包构建工具 TurboPack tsup
  - npm 包发布工具 np
- 实现 create
  - 读取 framework
  - 读取 template
  - 读取 remote
  - 本地预设模板
    - 每次更新模板都要重新修改包文件,不合理,适用于经常不变的模板,修改幅度小
  - 远程拉取模板
    - 采用 giget 拉去 github gitlab 等公共仓库模板
    - 采用 simple-git 拉取公司内部 gitlab 模板(是否需要权限)
      - http://gitlab-ci-token:<your_token>@11.122.1.27/cs-v3/cs.git
    - 或者通过 npminstall 拉取 npm 仓库的包文件
      - 适用于自己 publish
  - 执行命令工作目录复制操作,执行 pkg 文件重写操作
- 实现 dev
  - 利用 node 进程模块 判断 pnpm 环境
  - 执行 pnpm dev 命令启动服务器

- 实现 build
  - 利用 node 进程模块 判断 pnpm 环境
  - 执行 pnpm build 命令进行打包
- 重构
  - 插件化
  - 抽离封装校验
  - 臃肿函数拆分,具备后期通用性
- 发布部署
  - 配合阿里云效私有仓库及 nrm 切换仓库地址及 np 发布自动化工具

- 完善后续
  - Ai 接入
  - 目前得模板都是采用create vite 后期可以考虑自己封装一个基于 Vue3 Vite 个人组件库 个人 hooks 库的版本 然后配合个人脚手架来实现一个命令搭建一个现代标准化项目
