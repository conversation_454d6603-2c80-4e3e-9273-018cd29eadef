{"name": "y-cli", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "scripts": {"lint": "eslint --fix", "prepare": "husky", "spellcheck": "cspell lint --dot --gitignore --color --cache --show-suggestions \"(packages|apps)/**/*.@(js|cjs|mjs|ts|tsx)\"", "commit": "git cz", "dev": "turbo run dev", "build": "turbo run build", "release": "turbo run release"}, "keywords": [], "author": "", "license": "ISC", "config": {"commitizen": {"path": "node_modules/cz-git"}}, "lint-staged": {"*.{md,json}": ["prettier --cache --write --no-error-on-unmatched-pattern"], "*.{js,jsx}": ["eslint --fix", "prettier --cache --write"], "*.{ts,tsx}": ["eslint --fix", "prettier --cache --parser=typescript --write"]}, "packageManager": "pnpm@9.12.2+sha512.22721b3a11f81661ae1ec68ce1a7b879425a1ca5b991c975b074ac220b187ce56c708fe5db69f4c962c989452eee76c82877f4ee80f474cebd61ee13461b6228", "devDependencies": {"typescript": "^5.8.3", "eslint": "^9.31.0", "prettier": "^3.6.2", "@eslint/js": "9.31.0", "globals": "16.3.0", "typescript-eslint": "^8.37.0", "@typescript-eslint/eslint-plugin": "^8.37.0", "eslint-plugin-simple-import-sort": "12.1.1", "cspell": "^9.1.5", "husky": "^9.1.7", "lint-staged": "^16.1.2", "commitizen": "^4.3.1", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "cz-git": "^1.6.1", "turbo": "^2.5.5", "np": "10.2.0"}}