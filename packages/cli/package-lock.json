{"name": "y-cli", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "y-cli", "version": "1.0.0", "license": "ISC", "dependencies": {"y-cli": "^1.0.0"}, "bin": {"y": "bin/y"}, "devDependencies": {"@types/fs-extra": "11.0.4", "@types/node": "^20.19.9", "@types/prompts": "2.4.9", "commander": "14.0.0", "consola": "3.4.2", "fs-extra": "11.3.0", "giget": "1.0.0", "np": "10.2.0", "ora": "8.2.0", "picocolors": "1.1.1", "prompts": "2.4.2", "tsup": "^7.3.0", "typescript": "^5.8.3"}}, "../../node_modules/.pnpm/@types+fs-extra@11.0.4/node_modules/@types/fs-extra": {"version": "11.0.4", "dev": true, "license": "MIT", "dependencies": {"@types/jsonfile": "*", "@types/node": "*"}}, "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node": {"version": "20.19.9", "dev": true, "license": "MIT", "dependencies": {"undici-types": "~6.21.0"}}, "../../node_modules/.pnpm/@types+prompts@2.4.9/node_modules/@types/prompts": {"version": "2.4.9", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*", "kleur": "^3.0.3"}}, "../../node_modules/.pnpm/commander@14.0.0/node_modules/commander": {"version": "14.0.0", "dev": true, "license": "MIT", "devDependencies": {"@eslint/js": "^9.4.0", "@types/jest": "^29.2.4", "@types/node": "^22.7.4", "eslint": "^9.17.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-jest": "^28.3.0", "globals": "^16.0.0", "jest": "^29.3.1", "prettier": "^3.2.5", "ts-jest": "^29.0.3", "tsd": "^0.31.0", "typescript": "^5.0.4", "typescript-eslint": "^8.12.2"}, "engines": {"node": ">=20"}}, "../../node_modules/.pnpm/consola@3.4.2/node_modules/consola": {"version": "3.4.2", "dev": true, "license": "MIT", "devDependencies": {"@clack/prompts": "^0.10.0", "@types/node": "^22.13.10", "@vitest/coverage-v8": "^3.0.9", "changelogen": "^0.6.1", "defu": "^6.1.4", "eslint": "^9.22.0", "eslint-config-unjs": "^0.4.2", "is-unicode-supported": "^2.1.0", "prettier": "^3.5.3", "sentencer": "^0.2.1", "std-env": "^3.8.1", "string-width": "^7.2.0", "typescript": "^5.8.2", "unbuild": "^3.5.0", "vitest": "^3.0.9"}, "engines": {"node": "^14.18.0 || >=16.10.0"}}, "../../node_modules/.pnpm/fs-extra@11.3.0/node_modules/fs-extra": {"version": "11.3.0", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "devDependencies": {"klaw": "^2.1.1", "klaw-sync": "^3.0.2", "minimist": "^1.1.1", "mocha": "^10.1.0", "nyc": "^15.0.0", "proxyquire": "^2.0.1", "read-dir-files": "^0.1.1", "standard": "^17.0.0"}, "engines": {"node": ">=14.14"}}, "../../node_modules/.pnpm/giget@1.0.0/node_modules/giget": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"colorette": "^2.0.19", "defu": "^6.1.1", "https-proxy-agent": "^5.0.1", "mri": "^1.2.0", "node-fetch-native": "^1.0.1", "pathe": "^1.0.0", "tar": "^6.1.12"}, "bin": {"giget": "dist/cli.mjs"}, "devDependencies": {"@types/node": "^18.11.9", "@types/tar": "^6.1.3", "@vitest/coverage-c8": "^0.25.2", "eslint": "^8.27.0", "eslint-config-unjs": "^0.0.2", "jiti": "^1.16.0", "standard-version": "^9.5.0", "typescript": "^4.8.4", "unbuild": "^0.9.4", "vitest": "^0.25.2"}}, "../../node_modules/.pnpm/np@10.2.0_@types+node@20.19.9_typescript@5.8.3/node_modules/np": {"version": "10.2.0", "dev": true, "license": "MIT", "dependencies": {"chalk": "^5.4.1", "chalk-template": "^1.1.0", "cosmiconfig": "^8.3.6", "del": "^8.0.0", "escape-goat": "^4.0.0", "escape-string-regexp": "^5.0.0", "execa": "^8.0.1", "exit-hook": "^4.0.0", "github-url-from-git": "^1.5.0", "hosted-git-info": "^8.0.2", "ignore-walk": "^7.0.0", "import-local": "^3.2.0", "inquirer": "^12.3.2", "is-installed-globally": "^1.0.0", "is-interactive": "^2.0.0", "is-scoped": "^3.0.0", "issue-regex": "^4.3.0", "listr": "^0.14.3", "listr-input": "^0.2.1", "log-symbols": "^7.0.0", "meow": "^13.2.0", "new-github-release-url": "^2.0.0", "npm-name": "^8.0.0", "onetime": "^7.0.0", "open": "^10.0.4", "p-memoize": "^7.1.1", "p-timeout": "^6.1.4", "path-exists": "^5.0.0", "pkg-dir": "^8.0.0", "read-package-up": "^11.0.0", "read-pkg": "^9.0.1", "rxjs": "^7.8.1", "semver": "^7.6.0", "symbol-observable": "^4.0.0", "terminal-link": "^3.0.0", "update-notifier": "^7.3.1"}, "bin": {"np": "source/cli.js"}, "devDependencies": {"@sindresorhus/is": "^7.0.1", "@types/semver": "^7.5.8", "ava": "^6.2.0", "common-tags": "^1.8.2", "esmock": "^2.7.0", "fs-extra": "^11.3.0", "map-obj": "^5.0.2", "sinon": "^19.0.2", "strip-ansi": "^7.1.0", "tempy": "^3.1.0", "write-package": "^7.1.0", "xo": "^0.60.0"}, "engines": {"bun": ">=1", "git": ">=2.11.0", "node": ">=18", "npm": ">=9", "pnpm": ">=8", "yarn": ">=1.7.0"}, "funding": {"url": "https://github.com/sindresorhus/np?sponsor=1"}}, "../../node_modules/.pnpm/ora@8.2.0/node_modules/ora": {"version": "8.2.0", "dev": true, "license": "MIT", "dependencies": {"chalk": "^5.3.0", "cli-cursor": "^5.0.0", "cli-spinners": "^2.9.2", "is-interactive": "^2.0.0", "is-unicode-supported": "^2.0.0", "log-symbols": "^6.0.0", "stdin-discarder": "^0.2.2", "string-width": "^7.2.0", "strip-ansi": "^7.1.0"}, "devDependencies": {"@types/node": "^22.5.0", "ava": "^5.3.1", "get-stream": "^9.0.1", "transform-tty": "^1.0.11", "tsd": "^0.31.1", "xo": "^0.59.3"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "../../node_modules/.pnpm/picocolors@1.1.1/node_modules/picocolors": {"version": "1.1.1", "dev": true, "license": "ISC"}, "../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts": {"version": "2.4.2", "dev": true, "license": "MIT", "dependencies": {"kleur": "^3.0.3", "sisteransi": "^1.0.5"}, "devDependencies": {"@babel/cli": "^7.12.1", "@babel/core": "^7.12.3", "@babel/plugin-proposal-object-rest-spread": "^7.12.1", "@babel/preset-env": "^7.12.1", "tap-spec": "^2.2.2", "tape": "^4.13.3"}, "engines": {"node": ">= 6"}}, "../../node_modules/.pnpm/tsup@7.3.0_typescript@5.8.3/node_modules/tsup": {"version": "7.3.0", "dev": true, "license": "MIT", "dependencies": {"bundle-require": "^4.0.0", "cac": "^6.7.12", "chokidar": "^3.5.1", "debug": "^4.3.1", "esbuild": "^0.19.2", "execa": "^5.0.0", "globby": "^11.0.3", "joycon": "^3.0.1", "postcss-load-config": "^4.0.1", "resolve-from": "^5.0.0", "rollup": "^4.0.2", "source-map": "0.8.0-beta.0", "sucrase": "^3.20.3", "tree-kill": "^1.2.2"}, "bin": {"tsup": "dist/cli-default.js", "tsup-node": "dist/cli-node.js"}, "devDependencies": {"@rollup/plugin-json": "6.0.1", "@swc/core": "1.2.218", "@types/debug": "4.1.7", "@types/flat": "5.0.2", "@types/fs-extra": "9.0.13", "@types/node": "14.18.12", "@types/resolve": "1.20.1", "colorette": "2.0.16", "consola": "2.15.3", "flat": "5.0.2", "fs-extra": "10.0.0", "postcss": "8.4.12", "postcss-simple-vars": "6.0.3", "prettier": "2.5.1", "resolve": "1.20.0", "rollup-plugin-dts": "6.1.0", "sass": "1.62.1", "strip-json-comments": "4.0.0", "svelte": "3.46.4", "svelte-preprocess": "5.0.3", "terser": "^5.16.0", "ts-essentials": "9.1.2", "tsconfig-paths": "3.12.0", "tsup": "7.1.0", "typescript": "5.0.2", "vitest": "0.28.4", "wait-for-expect": "3.0.2"}, "engines": {"node": ">=18"}, "peerDependencies": {"@swc/core": "^1", "postcss": "^8.4.12", "typescript": ">=4.5.0"}, "peerDependenciesMeta": {"@swc/core": {"optional": true}, "postcss": {"optional": true}, "typescript": {"optional": true}}}, "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript": {"version": "5.8.3", "dev": true, "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "devDependencies": {"@dprint/formatter": "^0.4.1", "@dprint/typescript": "0.93.3", "@esfx/canceltoken": "^1.0.0", "@eslint/js": "^9.17.0", "@octokit/rest": "^21.0.2", "@types/chai": "^4.3.20", "@types/diff": "^5.2.3", "@types/minimist": "^1.2.5", "@types/mocha": "^10.0.10", "@types/ms": "^0.7.34", "@types/node": "latest", "@types/source-map-support": "^0.5.10", "@types/which": "^3.0.4", "@typescript-eslint/rule-tester": "^8.18.1", "@typescript-eslint/type-utils": "^8.18.1", "@typescript-eslint/utils": "^8.18.1", "azure-devops-node-api": "^14.1.0", "c8": "^10.1.3", "chai": "^4.5.0", "chalk": "^4.1.2", "chokidar": "^3.6.0", "diff": "^5.2.0", "dprint": "^0.47.6", "esbuild": "^0.24.0", "eslint": "^9.17.0", "eslint-formatter-autolinkable-stylish": "^1.4.0", "eslint-plugin-regexp": "^2.7.0", "fast-xml-parser": "^4.5.1", "glob": "^10.4.5", "globals": "^15.13.0", "hereby": "^1.10.0", "jsonc-parser": "^3.3.1", "knip": "^5.41.0", "minimist": "^1.2.8", "mocha": "^10.8.2", "mocha-fivemat-progress-reporter": "^0.1.0", "monocart-coverage-reports": "^2.11.4", "ms": "^2.1.3", "playwright": "^1.49.1", "source-map-support": "^0.5.21", "tslib": "^2.8.1", "typescript": "^5.7.2", "typescript-eslint": "^8.18.1", "which": "^3.0.1"}, "engines": {"node": ">=14.17"}}, "node_modules/@types/fs-extra": {"resolved": "../../node_modules/.pnpm/@types+fs-extra@11.0.4/node_modules/@types/fs-extra", "link": true}, "node_modules/@types/node": {"resolved": "../../node_modules/.pnpm/@types+node@20.19.9/node_modules/@types/node", "link": true}, "node_modules/@types/prompts": {"resolved": "../../node_modules/.pnpm/@types+prompts@2.4.9/node_modules/@types/prompts", "link": true}, "node_modules/commander": {"resolved": "../../node_modules/.pnpm/commander@14.0.0/node_modules/commander", "link": true}, "node_modules/consola": {"resolved": "../../node_modules/.pnpm/consola@3.4.2/node_modules/consola", "link": true}, "node_modules/fs-extra": {"resolved": "../../node_modules/.pnpm/fs-extra@11.3.0/node_modules/fs-extra", "link": true}, "node_modules/giget": {"resolved": "../../node_modules/.pnpm/giget@1.0.0/node_modules/giget", "link": true}, "node_modules/np": {"resolved": "../../node_modules/.pnpm/np@10.2.0_@types+node@20.19.9_typescript@5.8.3/node_modules/np", "link": true}, "node_modules/ora": {"resolved": "../../node_modules/.pnpm/ora@8.2.0/node_modules/ora", "link": true}, "node_modules/picocolors": {"resolved": "../../node_modules/.pnpm/picocolors@1.1.1/node_modules/picocolors", "link": true}, "node_modules/prompts": {"resolved": "../../node_modules/.pnpm/prompts@2.4.2/node_modules/prompts", "link": true}, "node_modules/tsup": {"resolved": "../../node_modules/.pnpm/tsup@7.3.0_typescript@5.8.3/node_modules/tsup", "link": true}, "node_modules/typescript": {"resolved": "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript", "link": true}, "node_modules/y-cli": {"version": "1.0.0", "resolved": "https://packages.aliyun.com/687e03c81da8ac0447be5d82/npm/Yuze/y-cli/-/y-cli-1.0.0.tgz", "integrity": "sha512-p7cGKO9B8lf1hXruBz1L65RIPCaSDfLFdKIUQtjW/4CRK7depyTv+e0YtaoFrSqqYX+S02J2zzSgWgvko9z4mg==", "license": "ISC", "bin": {"y": "bin/y"}}}}