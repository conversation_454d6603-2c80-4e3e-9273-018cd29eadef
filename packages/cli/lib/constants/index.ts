export const vueTemplateChoices = [
  {
    title: 'Vue TypeScript',
    value: 'vue-ts'
  },
  {
    title: 'Vue JavaScript',
    value: 'vue'
  }
]
export const reactTemplateChoices = [
  {
    title: 'React TypeScript',
    value: 'react-ts'
  },
  {
    title: 'React JavaScript',
    value: 'react'
  }
]
export const vanillaTemplateChoices = [
  {
    title: 'Vanilla TypeScript',
    value: 'vanilla-ts'
  },
  {
    title: 'Vanilla JavaScript',
    value: 'vanilla'
  }
]
export const h5TemplateChoices = [
  // {
  //   title: 'h5 Vue3 Vant',
  //   value: 'vanilla-ts'
  // },
  {
    title: 'H5 Asd',
    value: 'h5-asd'
  }
]
export const templateChoices = {
  vue: vueTemplateChoices,
  react: reactTemplateChoices,
  vanilla: vanillaTemplateChoices,
  h5: h5TemplateChoices
}
export const frameworkChoices = [
  {
    title: 'vue',
    value: 'vue'
  },
  {
    title: 'react',
    value: 'react'
  },
  {
    title: 'vanilla',
    value: 'vanilla'
  },
  {
    title: 'h5',
    value: 'h5'
  }
]
export const frameworks = ['vue', 'react', 'vanilla','h5'] as const

export const templates = ['vue-ts', 'vue', 'react-ts', 'react', 'vanilla-ts', 'vanilla','h5-asd'] as const
