import { ensureDir, writeFile } from 'fs-extra'
import path from 'path'

import { toPascalCase } from '../../utils/toPascalCase'

export async function generateReactFile(modulePath: string, baseDir: string) {
  
  await ensureDir(baseDir)

  await Promise.all([
    writeFile(
      path.join(baseDir, 'index.tsx'),
      `import React from 'react'\n\nimport cs from 'classnames'\n\nimport ./index.module.scss\n\nexport default function ${toPascalCase(modulePath)}() {\n  return <div>Hello ${modulePath}</div>\n}\n`
    ),
    writeFile(
      path.join(baseDir, 'types.ts'),
      `// ${modulePath} types\n\nexport interface ${toPascalCase(modulePath)}Data {\n  // TODO: define\n}`
    ),

    writeFile(
      path.join(baseDir, 'index.module.scss'),
      `/* ${modulePath} styles */\n\n.${modulePath.replace(/\//g, '-')} {\n  // TODO: style\n}`
    )
  ])
}
