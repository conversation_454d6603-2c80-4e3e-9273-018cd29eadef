import { ensureDir, writeFile } from 'fs-extra'
import path from 'path'

export async function generateVue2File(modulePath: string, baseDir: string) {
  await ensureDir(baseDir)

  await Promise.all([
    writeFile(
      path.join(baseDir, 'index.vue'),
      `<template>\n  <div class="${modulePath.replace(/\//g, '-')}">Hello ${modulePath}</div>\n</template>\n\n<script>\nexport default {\n}\n\n</script>\n\n<style lang="scss" scoped>\n</style>\n`
    )
  ])
}
