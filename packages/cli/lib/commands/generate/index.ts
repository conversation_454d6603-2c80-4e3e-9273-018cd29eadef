import type { Command } from 'commander'
import {  exists } from 'fs-extra'
import path from 'path'
import pc from 'picocolors'
import prompts from 'prompts'

import { logger } from '../../utils/logger'
import { createModule } from './createModule'
export default function createCommandPluginGenerate(program: Command) {
  return program
    .createCommand('generate')
    .description('generate module')
    .arguments('<modulePath>')
    .option('-f, --framework <framework>', 'framework')
    .action(async (modulePath: string, options: { framework: string }) => {
      /** 找到要创建的位置 */
      const baseDir = path.resolve(process.cwd(), 'src/views', modulePath)
      /** 判断是否存在 */
      if (await exists(baseDir)) {
        logger.error(pc.red(`目录${modulePath}已经存在`))
        process.exit(1)
      }
      if (!options.framework) {
        const res = await prompts({
          type: 'select',
          name: 'framework',
          message: 'Select framework',
          choices: [
            { title: 'vue2', value: 'vue2' },
            { title: 'vue3', value: 'vue3' },
            { title: 'react', value: 'react' }
          ]
        })
        options.framework = res.framework
      }
      await createModule(modulePath, baseDir, options.framework)
      logger.success(pc.green(`✅ 模块 ${pc.bold(modulePath)} 创建成功 at src/views/${modulePath}`))
    })
}
