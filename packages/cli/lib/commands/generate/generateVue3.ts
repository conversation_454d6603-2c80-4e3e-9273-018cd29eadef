import { ensureDir, writeFile } from 'fs-extra'
import path from 'path'

import { toPascalCase } from '../../utils/toPascalCase'

export async function generateVue3File(modulePath: string, baseDir: string) {
  await ensureDir(baseDir)

  await Promise.all([
    writeFile(
      path.join(baseDir, 'index.vue'),
      `
<template>
  <div class="${modulePath.replace(/\//g, '-')}">Hello ${modulePath}</div>
</template>

<script setup lang="ts">
// TODO
</script>

<style lang="scss" scoped>
@import './index.scss';
</style>`
    ),

    writeFile(
      path.join(baseDir, 'hooks.ts'),
      `export function use${toPascalCase(modulePath)}() {\n  // TODO\n}`
    ),

    writeFile(
      path.join(baseDir, 'index.scss'),
      `.${modulePath.replace(/\//g, '-')} {\n  // TODO: style\n}`
    ),

    writeFile(
      path.join(baseDir, 'types.ts'),
      `export interface ${toPascalCase(modulePath)}Data {\n  // TODO: define\n}`
    )
  ])
}
