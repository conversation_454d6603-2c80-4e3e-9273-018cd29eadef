import { generateReactFile } from './generateReact'
import { generateVue2File } from './generateVue2'
import { generateVue3File } from './generateVue3'

export async function createModule(modulePath: string, baseDir: string, framework: string) {
  switch (framework) {
    case 'vue2':
      return generateVue2File(modulePath,baseDir)
    case 'vue3':
      return generateVue3File(modulePath,baseDir)
    case 'react':
      return generateReactFile(modulePath,baseDir)
    default:
      throw new Error(`Unknown framework: ${framework}`)
  }
}
