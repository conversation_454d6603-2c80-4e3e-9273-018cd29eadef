import globals from 'globals'
import js from '@eslint/js'
import tseslint from 'typescript-eslint'
import importSort from 'eslint-plugin-simple-import-sort'
import eslintPluginPrettier from 'eslint-plugin-prettier'
/** 后期可以拓展
 * 1. ts 基础库包
 * 2. vue 基础库包
 * 3. react 基础库包
 * 4. vue2 js 基础库包
 * 5. vue3 js 基础库包
 */

export default tseslint.config({
  extends: [js.configs.recommended, ...tseslint.configs.recommended],
  files: ['**/*.{js,jsx,ts,tsx}'],
  ignores: ['*.js', '**/*dist/**/*', '**/*node_modules/**/*'],
  rules: {
    'no-console': 'error',
    'prettier/prettier': 'error',
    'simple-import-sort/imports': [
      'error',
      {
        groups: [['^\\w'], ['^@\\w'], ['^@/'], ['^\\u0000']]
      }
    ]
  },
  languageOptions: {
    parser: tseslint.parser,
    globals: {
      ...globals.node
    },
    parserOptions: {
      tsconfigRootDir: import.meta.dirname
    }
  },
  plugins: { 'simple-import-sort': importSort, 'prettier': eslintPluginPrettier }
})
