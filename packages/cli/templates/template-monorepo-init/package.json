{"name": "@y-monitor/root", "version": "1.0.0", "description": "", "private": true, "type": "module", "scripts": {"commit": "git cz", "lint": "eslint  --fix", "spellcheck": "cspell lint --dot --gitignore --color --cache --show-suggestions \"(packages|apps)/**/*.@(js|cjs|mjs|ts|tsx)\"", "prepare": "husky"}, "keywords": [], "author": "", "license": "ISC", "config": {"commitizen": {"path": "node_modules/cz-git"}}, "lint-staged": {"*.{md,json}": ["prettier --cache --write --no-error-on-unmatched-pattern"], "*.{js,jsx}": ["eslint --fix", "prettier --cache --write"], "*.{ts,tsx}": ["eslint --fix", "prettier --cache --parser=typescript --write"]}, "devDependencies": {"eslint": "^9.18.0", "prettier": "^3.4.2", "@eslint/js": "^9.18.0", "@typescript-eslint/parser": "^8.19.1", "eslint-plugin-prettier": "^5.2.3", "globals": "^15.12.0", "typescript": "^5.7.3", "typescript-eslint": "^8.19.1", "eslint-plugin-simple-import-sort": "^12.1.1", "cspell": "^8.10.0", "husky": "^9.1.7", "lint-staged": "^16.1.2", "commitizen": "^4.3.1", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "cz-git": "^1.6.1"}, "packageManager": "pnpm@9.12.2"}