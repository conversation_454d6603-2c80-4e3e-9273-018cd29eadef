{"name": "vue2-project-plugin", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "syncapi": "vue-cli-service build --mode syncapi", "build:prod": "vue-cli-service build --mode production", "build:test": "vue-cli-service build --mode test", "build:test:h5": "VUE_APP_IS_H5=true vue-cli-service build --mode test "}, "dependencies": {"amfe-flexible": "^2.2.1", "axios": "^1.10.0", "core-js": "^3.6.5", "crypto-js": "^4.2.0", "html-webpack-plugin": "^3.2.0", "js-calendar-converter": "0.0.7", "lunar-javascript": "^1.7.3", "moment": "^2.30.1", "node-sass": "^4.14.1", "normalize.css": "^8.0.1", "nprogress": "^0.2.0", "pinia": "^2.3.1", "postcss-pxtorem": "^5.1.1", "puppeteer": "^19.11.1", "qs": "^6.14.0", "sass-loader": "^7.1.0", "tesseract.js": "^6.0.1", "vant": "^2.13.8", "vue": "^2.7.16", "vue-router": "^3.6.5", "vue-template-compiler": "^2.7.16"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.19", "@vue/cli-plugin-eslint": "~4.5.19", "@vue/cli-service": "~4.5.18", "@vue/eslint-config-prettier": "^5.0.0", "babel-eslint": "^10.0.1", "eslint": "^7.32.0", "eslint-plugin-prettier": "^3.1.0", "eslint-plugin-vue": "^9.0.0", "prettier": "^1.18.2", "vue-loader": "^15.9.8"}}