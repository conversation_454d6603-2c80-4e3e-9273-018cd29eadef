<template>
  <div id="app">
    <div :class="showTab ? 'content' : 'content-no-tabbar'">
      <router-view />
    </div>
    <Tabar v-if="showTab" :activePath="activePath" />
  </div>
</template>

<script setup>
import Tabar from "@/components/tabbar/index.vue";
import { getCurrentInstance, watch, ref } from "vue";
const { proxy } = getCurrentInstance();
const showTabLIst = ref(["/", "/activity", "/need", "/mine"]);
const showTab = ref(false);
const activePath = ref("/");
watch(
  () => proxy.$route.fullPath, // 监听路由路径变化
  val => {
    console.log("滚动到顶部", val);
    showTab.value = showTabLIst.value.includes(val);
    activePath.value = val;
  },
  { immediate: true }
);
</script>
<script>
export default {
  name: "App"
};
</script>
<style lang="scss" scoped>
#app {
  font-family: "Avenir", Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  // height: 100vh;
  // overflow: hidden;
}
.content {
  height: calc(100vh - 56px);
  // overflow-y: hidden;
  // padding-bottom: 56px;
  box-sizing: border-box;
}
.content-no-tabbar {
  height: 100%;
}
:deep(.van-button--primary) {
  background-color: #3f7ffc;
  border: 1px solid #3f7ffc;
}
:deep(.van-hairline--bottom::after) {
  border-bottom-width: 0;
}
</style>
