<template>
  <div class="financial-market">
    <!-- 头部导航 -->
    <van-nav-bar
      title="金融超市"
      left-arrow
      @click-left="onClickLeft"
      class="custom-nav-bar"
    >
      <template #right>
        <van-icon name="ellipsis" class="nav-menu" />
      </template>
    </van-nav-bar>

    <!-- 搜索栏 -->
    <div class="search-section">
      <van-search
        v-model="searchValue"
        placeholder="请输入关键搜索词"
        shape="round"
        background="transparent"
        @search="onSearch"
      />
    </div>

    <!-- 筛选标签 -->
    <div class="filter-tabs">
      <van-tabs v-model="activeTab" @click="onTabClick">
        <van-tab title="全部产品" name="all" />
        <van-tab title="贷款金额" name="amount" />
        <van-tab title="贷款期限" name="period" />
        <van-tab title="筛选" name="filter" />
      </van-tabs>
    </div>

    <!-- 产品列表 -->
    <div class="product-list">
      <product-card
        v-for="(product, index) in productList"
        :key="index"
        :product="product"
        @apply="onApplyLoan"
      />
    </div>
  </div>
</template>

<script>
import ProductCard from "@/components/ProductCard";

export default {
  name: "FinancialMarket",
  components: {
    ProductCard
  },
  data() {
    return {
      searchValue: "",
      activeTab: "all",
      productList: [
        {
          name: "农担贷",
          interestRate: "3.7-6.8%",
          amount: "10万-1000万",
          period: "12个月",
          rateLabel: "参考利率",
          amountLabel: "贷款额度(元)",
          periodLabel: "还款期限",
          tags: ["担保贷款", "扶持农业"],
          bankName: "农业发展银行",
          bankLogo:
            "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/image-cefTGIrG8SjslimrPxcsXaRMuz5zSR.png"
        },
        {
          name: "齐鲁富民贷",
          interestRate: "3.75%",
          amount: "10万-300万",
          period: "12个月",
          rateLabel: "参考利率",
          amountLabel: "贷款额度(元)",
          periodLabel: "还款期限",
          tags: ["信用贷款", "扶持农业"],
          bankName: "中国农业银行",
          bankLogo:
            "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/image-cefTGIrG8SjslimrPxcsXaRMuz5zSR.png"
        },
        {
          name: "助农贷",
          interestRate: "3.75-6.4%",
          amount: "10万-500万",
          period: "12个月",
          rateLabel: "参考利率",
          amountLabel: "贷款额度(元)",
          periodLabel: "还款期限",
          tags: ["信用贷款", "扶持农业"],
          bankName: "中国农业银行",
          bankLogo:
            "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/image-cefTGIrG8SjslimrPxcsXaRMuz5zSR.png"
        }
      ]
    };
  },
  methods: {
    onClickLeft() {
      this.$router.go(-1);
    },
    onSearch(value) {
      console.log("搜索:", value);
      // 实现搜索逻辑
    },
    onTabClick(name, title) {
      console.log("切换标签:", name, title);
      // 实现筛选逻辑
    },
    onApplyLoan(product) {
      console.log("申请贷款:", product);
      // 跳转到申请页面
      this.$router.push({
        name: "LoanApplication",
        params: { productId: product.id }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.financial-market {
  min-height: 100vh;
  background-color: #f7f8fa;

  .custom-nav-bar {
    position: sticky;
    top: 0;
    background-color: white;

    :deep(.van-nav-bar__title) {
      color: #323233;
      font-weight: 500;
    }

    .nav-menu {
      color: #969799;
      font-size: 18px;
    }
  }

  .search-section {
    padding: 12px 16px;
    background-color: white;

    :deep(.van-search) {
      padding: 0;

      .van-search__content {
        background-color: #f7f8fa;
        border-radius: 20px;
      }

      .van-field__control {
        font-size: 14px;
      }
    }
  }

  .filter-tabs {
    background-color: white;

    :deep(.van-tabs) {
      .van-tabs__wrap {
        padding: 0 16px;
      }

      .van-tab {
        font-size: 14px;
        color: #646566;

        &.van-tab--active {
          color: #323233;
          font-weight: 500;
        }
      }

      .van-tabs__line {
        background-color: #1989fa;
      }
    }
  }

  .product-list {
    padding: 12px 16px;
  }
}
</style>
