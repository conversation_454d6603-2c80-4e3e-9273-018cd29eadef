<template>
  <div class="financial-market">
    <!-- 头部导航 -->
    <van-nav-bar
      title="金融超市"
      left-arrow
      @click-left="onClickLeft"
      class="custom-nav-bar"
    >
      <template #right>
        <van-icon name="ellipsis" class="nav-menu" />
      </template>
    </van-nav-bar>

    <!-- 搜索栏和筛选栏固定 -->
    <div class="fixed-header">
      <div class="search-section">
        <van-search
          v-model="searchValue"
          placeholder="请输入关键搜索词"
          shape="round"
          background="transparent"
          @search="onSearch"
        />
      </div>
      <div class="filter-dropdown">
        <van-dropdown-menu>
          <van-dropdown-item
            v-model="filterOptions.category"
            :options="categoryOptions"
            title="全部产品"
            @change="val => onFilterChange('category', val)"
          />
          <van-dropdown-item
            v-model="filterOptions.amount"
            :options="amountOptions"
            title="贷款金额"
            @change="val => onFilterChange('amount', val)"
          />
          <van-dropdown-item
            v-model="filterOptions.period"
            :options="periodOptions"
            title="贷款期限"
            @change="val => onFilterChange('period', val)"
          />
          <van-dropdown-item title="筛选" v-if="false" ref="filterDropdown">
            <div class="custom-filter">
              <!-- 产品类型 多选 -->
              <div class="filter-section">
                <h4>产品类型</h4>
                <div class="filter-options">
                  <div
                    v-for="type in categoryOptions"
                    :key="type.value"
                    class="filter-option"
                    :class="{
                      active: filterOptions.categoryMulti.includes(type.value)
                    }"
                    @click="toggleFilterOption('categoryMulti', type.value)"
                  >
                    {{ type.text }}
                  </div>
                </div>
              </div>

              <!-- 贷款金额 多选 -->
              <div class="filter-section">
                <h4>贷款金额</h4>
                <div class="filter-options">
                  <div
                    v-for="amount in amountOptions"
                    :key="amount.value"
                    class="filter-option"
                    :class="{
                      active: filterOptions.amountMulti.includes(amount.value)
                    }"
                    @click="toggleFilterOption('amountMulti', amount.value)"
                  >
                    {{ amount.text }}
                  </div>
                </div>
              </div>

              <!-- 贷款期限 多选 -->
              <div class="filter-section">
                <h4>贷款期限</h4>
                <div class="filter-options">
                  <div
                    v-for="period in periodOptions"
                    :key="period.value"
                    class="filter-option"
                    :class="{
                      active: filterOptions.periodMulti.includes(period.value)
                    }"
                    @click="toggleFilterOption('periodMulti', period.value)"
                  >
                    {{ period.text }}
                  </div>
                </div>
              </div>

              <!-- 操作按钮 -->
              <div class="filter-actions">
                <van-button @click="resetCustomFilter">重置</van-button>
                <van-button type="primary" @click="applyCustomFilter"
                  >确定</van-button
                >
              </div>
            </div>
          </van-dropdown-item>
        </van-dropdown-menu>
      </div>
    </div>

    <!-- 产品列表区域独立滚动 -->
    <div class="scroll-area">
      <van-list
        v-model="loading"
        :finished="finished"
        :finished-text="filteredProductList.length ? '没有更多了' : ''"
        @load="onLoadMore"
      >
        <div class="product-list">
          <product-card
            v-for="(product, index) in filteredProductList"
            :key="index"
            :product="product"
            @apply="onApplyLoan"
          />
          <!-- 空状态 -->
          <van-empty
            v-if="!loading && filteredProductList.length === 0"
            description="暂无符合条件的产品"
            image="search"
          />
        </div>
      </van-list>
    </div>
  </div>
</template>

<script>
import ProductCard from "@/components/ProductCard";
import { getProductList, getProductType } from "@/api/product";

export default {
  name: "FinancialMarket",
  components: {
    ProductCard
  },
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        data: {}
      },
      searchValue: "",
      // 单选：顶部下拉筛选
      filterOptions: {
        category: "",
        amount: "",
        period: "",
        // 多选：弹窗自定义筛选
        categoryMulti: [],
        amountMulti: [],
        periodMulti: []
      },
      selectedFeatures: [],
      refreshing: false,
      loading: false,
      finished: false,
      productList: [],
      // 下拉选项数据
      categoryOptions: [{ text: "全部产品", value: "" }],
      amountOptions: [
        { text: "贷款金额", value: "" },
        { text: "10万以下", value: "0-10" },
        { text: "10-50万", value: "10-50" },
        { text: "50-100万", value: "50-100" },
        { text: "100-500万", value: "100-500" },
        { text: "500万以上", value: "500" }
      ],
      periodOptions: [
        { text: "贷款期限", value: "" },
        { text: "6个月以内", value: "0-6" },
        { text: "6-12个月", value: "6-12" },
        { text: "1-2年", value: "12-24" },
        { text: "2-3年", value: "24-36" },
        { text: "3年以上", value: "36" }
      ],
      rateRangeOptions: [
        { text: "3%以下", value: "0-3" },
        { text: "3-5%", value: "3-5" },
        { text: "5-7%", value: "5-7" },
        { text: "7%以上", value: "7" }
      ],
      bankTypeOptions: [
        { text: "国有银行", value: "state" },
        { text: "股份制银行", value: "joint" },
        { text: "城商行", value: "city" },
        { text: "农商行", value: "rural" }
      ],
      featureOptions: [
        { text: "快速放款", value: "fast" },
        { text: "免担保", value: "no-guarantee" },
        { text: "低利率", value: "low-rate" },
        { text: "扶持农业", value: "agriculture-support" }
      ]
    };
  },
  created() {
    // 只获取产品类型，不加载列表数据
    this._getProductType();
  },
  mounted() {
    // 在组件挂载后手动触发初始数据加载
    this.resetAndLoad();
  },
  computed: {
    filteredProductList() {
      // 这里只做简单过滤，实际可根据 filterOptions/selectedFeatures 进一步过滤
      return this.productList;
    }
  },
  methods: {
    _getProductType() {
      getProductType().then(res => {
        this.categoryOptions = this.categoryOptions.concat(
          res ? res.map(item => ({ text: item.cpmc, value: item.cpmc })) : []
        );
      });
    },
    // 顶部下拉筛选（单选）
    onFilterChange(type, val) {
      this.filterOptions[type] = val;
      // 只用单选条件
      this.queryParams.data = {
        cpmc: this.filterOptions.category,
        dked: this.filterOptions.amount,
        dkzq: this.filterOptions.period
      };
      this.resetAndLoad();
    },
    // 弹窗多选切换
    toggleFilterOption(type, value) {
      const arr = this.filterOptions[type];
      const idx = arr.indexOf(value);
      if (idx > -1) arr.splice(idx, 1);
      else arr.push(value);
    },
    // 弹窗重置
    resetCustomFilter() {
      this.filterOptions.categoryMulti = [];
      this.filterOptions.amountMulti = [];
      this.filterOptions.periodMulti = [];
    },
    // 弹窗确定，合并多选条件，调用查询
    applyCustomFilter() {
      this.$refs.filterDropdown.toggle();
      // 多选条件合并到data
      this.queryParams.data = {
        cpmcMulti: this.filterOptions.categoryMulti,
        dkedMulti: this.filterOptions.amountMulti,
        dkzqMulti: this.filterOptions.periodMulti
      };
      this.resetAndLoad();
    },
    async resetAndLoad() {
      this.queryParams.pageNum = 1;
      this.finished = false;
      this.productList = [];
      await this._getProductList();
    },
    async _getProductList() {
      if (this.finished) return;
      this.loading = true;
      try {
        const { records, total } = await getProductList({
          pageNum: this.queryParams.pageNum,
          pageSize: this.queryParams.pageSize,
          data: this.queryParams.data
        });
        if (this.queryParams.pageNum === 1) {
          this.productList = records;
        } else {
          this.productList = this.productList.concat(records);
        }
        this.queryParams.total = total;
        // 判断是否加载完成
        if (this.productList.length >= total) {
          this.finished = true;
        }
        this.loading = false;
        this.refreshing = false;
      } catch (error) {
        this.loading = false;
        this.refreshing = false;
      }
    },
    onRefresh() {
      this.resetAndLoad();
    },
    onLoadMore() {
      if (this.finished) return;
      this.queryParams.pageNum++;
      this._getProductList();
    },
    onClickLeft() {
      this.$router.go(-1);
    },
    onSearch(value) {
      this.queryParams.data.cpmc = value;
      this.resetAndLoad();
    },
    // 下面方法保持不变
    selectRate(value) {},
    selectBankType(value) {},
    toggleFeature(value) {
      const index = this.selectedFeatures.indexOf(value);
      if (index > -1) {
        this.selectedFeatures.splice(index, 1);
      } else {
        this.selectedFeatures.push(value);
      }
    },
    onApplyLoan(product) {
      this.$router.push({
        path: "/productDetail",
        query: { product: JSON.stringify(product) }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.financial-market {
  background-color: #f7f8fa;
  display: flex;
  flex-direction: column;

  height: 100vh;
  overflow: hidden;

  .fixed-header {
    position: sticky;
    top: 0;
    z-index: 10;
    background: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.02);
  }

  .scroll-area {
    flex: 1;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    background: #f7f8fa;
  }

  .custom-nav-bar {
    position: sticky;
    top: 0;
    background-color: white;

    :deep(.van-nav-bar__title) {
      color: #323233;
      font-weight: 500;
    }

    .nav-menu {
      color: #969799;
      font-size: 18px;
    }
  }

  .search-section {
    padding: 12px 16px;
    background-color: white;

    :deep(.van-search) {
      padding: 0;

      .van-search__content {
        background-color: #f7f8fa;
        border-radius: 20px;
      }

      .van-field__control {
        font-size: 14px;
      }
    }
  }

  .filter-dropdown {
    background-color: white;
    border-bottom: 1px solid #ebedf0;

    :deep(.van-dropdown-menu) {
      .van-dropdown-menu__bar {
        height: 48px;
        box-shadow: none;
      }

      .van-dropdown-menu__title {
        font-size: 14px;
        color: #646566;

        &.van-dropdown-menu__title--active {
          color: #1989fa;
        }
      }
    }

    .custom-filter {
      padding: 16px;
      max-height: 60vh;
      overflow-y: auto;

      .filter-section {
        margin-bottom: 20px;

        h4 {
          margin: 0 0 12px 0;
          font-size: 14px;
          font-weight: 500;
          color: #323233;
        }

        .filter-options {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;

          .filter-option {
            padding: 6px 12px;
            border: 1px solid #ebedf0;
            border-radius: 16px;
            font-size: 12px;
            color: #646566;
            background-color: white;
            cursor: pointer;
            transition: all 0.2s;

            &:active {
              transform: scale(0.95);
            }

            &.active {
              background-color: #1989fa;
              border-color: #1989fa;
              color: white;
            }
          }
        }
      }

      .filter-actions {
        display: flex;
        gap: 12px;
        padding-top: 16px;
        border-top: 1px solid #ebedf0;

        .van-button {
          flex: 1;
          height: 40px;
        }
      }
    }
  }

  .product-list {
    padding: 12px 16px;
    height: 100%;

    :deep(.van-empty) {
      padding: 60px 0;
    }
  }
}

// 响应式适配
@media (max-width: 375px) {
  .financial-market {
    .filter-dropdown {
      .custom-filter {
        padding: 12px;

        .filter-section {
          .filter-options {
            .filter-option {
              padding: 4px 4px;
              font-size: 11px;
            }
          }
        }
      }
    }
  }
}
</style>
