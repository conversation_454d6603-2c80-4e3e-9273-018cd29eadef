<template>
  <div class="product-detail">
    <!-- 头部导航 -->
    <van-nav-bar
      title="产品详情"
      left-arrow
      @click-left="onClickLeft"
      class="custom-nav-bar"
    >
    </van-nav-bar>

    <!-- 产品头部信息 -->
    <product-header :product="product" />

    <!-- 银行信息 -->
    <bank-info :bank="product" />

    <!-- 产品详情内容 -->
    <div class="product-content">
      <!-- 产品介绍 -->
      <product-info-section title="产品介绍" :content="product.cpms" />

      <!-- 担保方式 -->
      <product-info-section
        title="担保方式"
        :content="getGuaranteeType(product)"
      />

      <!-- 融资用途 -->
      <product-info-section title="融资用途" :content="financingPurpose" />

      <!-- 产品特点 -->
      <!-- <product-features-section :features="productFeatures" /> -->

      <!-- 申请条件 -->
      <product-requirements-section :requirements="product.sqtj.split('\n')" />

      <!-- 申请材料 -->
      <application-process-section :content="product.sqcl" />
    </div>

    <!-- 底部申请按钮 -->
    <div class="bottom-action">
      <van-button
        type="primary"
        block
        round
        @click="applyNow"
        class="apply-button"
      >
        立即申请
      </van-button>
    </div>

    <!-- 操作菜单 -->
    <van-action-sheet
      v-model="showActionSheet"
      :actions="actionSheetActions"
      @select="onActionSelect"
      cancel-text="取消"
    />
  </div>
</template>

<script>
import ProductHeader from "./components/ProductHeader.vue";
import BankInfo from "./components/BankInfo.vue";
import ProductIntroSection from "./components/ProductIntroSection.vue";
import ProductInfoSection from "./components/ProductInfoSection.vue";
import ProductFeaturesSection from "./components/ProductFeaturesSection.vue";
import ProductRequirementsSection from "./components/ProductRequirementsSection.vue";
import ApplicationProcessSection from "./components/ApplicationProcessSection.vue";

export default {
  name: "ProductDetail",
  components: {
    ProductHeader,
    BankInfo,
    ProductIntroSection,
    ProductInfoSection,
    ProductFeaturesSection,
    ProductRequirementsSection,
    ApplicationProcessSection
  },
  data() {
    return {
      product: {},
      showActionSheet: false,
      productInfo: {
        financingAmount: "1-1000万",
        interestRate: "2.95%-4.5%",
        amountLabel: "融资额度",
        rateLabel: "参考利率"
      },
      bankInfo: {
        name: "青岛银行股份有限公司",
        shortName: "青岛银行",
        logo: "/placeholder.svg?height=32&width=32&text=青岛银行"
      },
      productIntroduction: {
        title: "产品介绍",
        content:
          '"人才快贷"业务，是指青岛银行结合各地级市政府"人才贷"风险补偿机制，支持山东省内高层次人才长期所在的小微企业日常经营周转和创新创业活动的标准化授信业务。额度最高1000万元。'
      },
      guaranteeMethod: "信用",
      financingPurpose: "用于小微企业日常经营周转和创新创业活动",
      productFeatures: [
        {
          title: "额度高",
          description: "最高可申请1000万元授信额度"
        },
        {
          title: "利率低",
          description: "享受政府风险补偿，利率优惠明显"
        },
        {
          title: "纯信用",
          description: "无需抵押担保，凭借人才资质即可申请"
        },
        {
          title: "审批快",
          description: "标准化流程，快速审批放款"
        }
      ],
      applicationRequirements: [
        "申请人为山东省内认定的高层次人才",
        "企业注册地在山东省内",
        "企业成立满1年，经营状况良好",
        "企业信用记录良好，无不良征信",
        "企业主营业务符合国家产业政策"
      ],
      applicationProcess: [
        {
          step: 1,
          title: "提交申请",
          description: "填写申请表，提交相关材料"
        },
        {
          step: 2,
          title: "资格审核",
          description: "银行审核人才资质和企业情况"
        },
        {
          step: 3,
          title: "实地调研",
          description: "银行实地了解企业经营状况"
        },
        {
          step: 4,
          title: "审批放款",
          description: "通过审批后签约放款"
        }
      ],
      actionSheetActions: [
        { name: "分享产品", value: "share" },
        { name: "收藏产品", value: "favorite" },
        { name: "咨询客服", value: "contact" }
      ]
    };
  },
  mounted() {
    const { product } = this.$route.query;
    if (product) this.product = JSON.parse(product);
  },
  methods: {
    getGuaranteeType() {
      let str = "";
      const guaranteeTypeMap = {
        "01": "抵押",
        "02": "质押",
        "03": "信用",
        "04": "信保基金",
        "05": "一般保证",
        "06": "连带责任保证"
      };
      console.log(this.product.dbfs.split(";"));
      this.product.dbfs
        .split(";")
        .filter(item => item)
        .forEach(item => {
          str += guaranteeTypeMap[item] + "," || "";
        });

      return str.slice(0, -1);
    },
    onClickLeft() {
      this.$router.go(-1);
    },
    onRefresh() {
      // 刷新页面数据
      this.$toast("刷新中...");
    },
    onClose() {
      // 关闭页面
      this.$router.go(-1);
    },
    applyNow() {
      // 跳转到申请页面
      this.$router.push({
        path: "/publish",
        query: { product: JSON.stringify(this.product) }
      });
    },
    onActionSelect(action) {
      switch (action.value) {
        case "share":
          this.shareProduct();
          break;
        case "favorite":
          this.toggleFavorite();
          break;
        case "contact":
          this.contactService();
          break;
      }
    },
    shareProduct() {
      this.$toast("分享功能");
    },
    toggleFavorite() {
      this.$toast("收藏功能");
    },
    contactService() {
      this.$toast("客服咨询");
    }
  }
};
</script>

<style lang="scss" scoped>
.product-detail {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 80px;

  .custom-nav-bar {
    position: sticky;
    top: 0;
    background-color: white;

    :deep(.van-nav-bar__title) {
      color: #323233;
      font-weight: 500;
      font-size: 16px;
    }

    .nav-actions {
      display: flex;
      gap: 12px;

      .nav-icon {
        color: #969799;
        font-size: 18px;
        cursor: pointer;

        &:active {
          opacity: 0.7;
        }
      }
    }
  }

  .product-content {
    padding: 0 16px;
  }

  .bottom-action {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 12px 16px;
    background-color: white;
    border-top: 1px solid #ebedf0;
    z-index: 100;

    .apply-button {
      height: 48px;
      font-size: 16px;
      font-weight: 600;
      background: #1989fa;
      border: none;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(25, 137, 250, 0.3);
    }
  }
}
</style>
