<template>
  <div class="product-features-section">
    <h3 class="section-title">产品特点</h3>
    <div class="features-grid">
      <div 
        v-for="(feature, index) in features" 
        :key="index"
        class="feature-item"
      >
        <div class="feature-icon">
          <van-icon :name="getFeatureIcon(index)" />
        </div>
        <div class="feature-content">
          <h4 class="feature-title">{{ feature.title }}</h4>
          <p class="feature-description">{{ feature.description }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ProductFeaturesSection',
  props: {
    features: {
      type: Array,
      required: true
    }
  },
  methods: {
    getFeatureIcon(index) {
      const icons = ['gold-coin-o', 'discount', 'shield-o', 'clock-o']
      return icons[index] || 'star-o'
    }
  }
}
</script>

<style lang="scss" scoped>
.product-features-section {
  background-color: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  
  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #323233;
    margin: 0 0 16px 0;
  }
  
  .features-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    
    .feature-item {
      display: flex;
      align-items: flex-start;
      
      .feature-icon {
        width: 32px;
        height: 32px;
        background: linear-gradient(135deg, #1989fa 0%, #1976d2 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 8px;
        flex-shrink: 0;
        
        .van-icon {
          color: white;
          font-size: 16px;
        }
      }
      
      .feature-content {
        flex: 1;
        
        .feature-title {
          font-size: 14px;
          font-weight: 600;
          color: #323233;
          margin: 0 0 4px 0;
        }
        
        .feature-description {
          font-size: 12px;
          color: #646566;
          line-height: 1.4;
          margin: 0;
        }
      }
    }
  }
}

@media (max-width: 375px) {
  .product-features-section {
    .features-grid {
      grid-template-columns: 1fr;
    }
  }
}
</style>
