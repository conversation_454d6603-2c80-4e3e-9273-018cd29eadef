<template>
  <div class="institution-intro-card">
    <div class="card-header">
      <div class="header-line"></div>
      <h3 class="card-title">{{ introduction.title }}</h3>
    </div>
    
    <div class="card-content">
      <div class="intro-text" :class="{ expanded: isExpanded }">
        {{ introduction.content }}
      </div>
      
      <div class="expand-button" v-if="showExpandButton" @click="toggleExpand">
        <span>{{ isExpanded ? '收起' : '展开' }}</span>
        <van-icon :name="isExpanded ? 'arrow-up' : 'arrow-down'" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'InstitutionIntroCard',
  props: {
    introduction: {
      type: Object,
      required: true,
      default: () => ({})
    }
  },
  data() {
    return {
      isExpanded: false,
      showExpandButton: false
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.checkTextOverflow()
    })
  },
  methods: {
    toggleExpand() {
      this.isExpanded = !this.isExpanded
    },
    checkTextOverflow() {
      const textElement = this.$el.querySelector('.intro-text')
      if (textElement && textElement.scrollHeight > textElement.clientHeight) {
        this.showExpandButton = true
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.institution-intro-card {
  margin: 12px 16px;
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  
  .card-header {
    padding: 16px 16px 0;
    display: flex;
    align-items: center;
    
    .header-line {
      width: 3px;
      height: 16px;
      background-color: #1989fa;
      border-radius: 2px;
      margin-right: 8px;
    }
    
    .card-title {
      font-size: 16px;
      font-weight: 600;
      color: #323233;
      margin: 0;
    }
  }
  
  .card-content {
    padding: 12px 16px 16px;
    
    .intro-text {
      font-size: 14px;
      line-height: 1.6;
      color: #646566;
      text-align: justify;
      max-height: 84px;
      overflow: hidden;
      transition: max-height 0.3s ease;
      
      &.expanded {
        max-height: none;
      }
    }
    
    .expand-button {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 12px;
      padding: 8px;
      color: #1989fa;
      font-size: 13px;
      cursor: pointer;
      transition: all 0.2s;
      
      &:active {
        opacity: 0.7;
      }
      
      .van-icon {
        margin-left: 4px;
        font-size: 12px;
      }
    }
  }
}

// 响应式适配
@media (max-width: 375px) {
  .institution-intro-card {
    margin: 8px 12px;
    
    .card-header {
      padding: 14px 14px 0;
      
      .card-title {
        font-size: 15px;
      }
    }
    
    .card-content {
      padding: 10px 14px 14px;
      
      .intro-text {
        font-size: 13px;
        max-height: 78px;
      }
    }
  }
}
</style>
