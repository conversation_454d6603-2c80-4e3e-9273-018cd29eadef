<template>
  <div class="application-process-section">
    <h3 class="section-title">申请材料</h3>
    <div class="process-timeline">
      <div class="content">{{ content }}</div>

      <!-- <div
        v-for="(step, index) in process"
        :key="index"
        class="process-step"
        :class="{ 'last-step': index === process.length - 1 }"
      >
        <div class="step-indicator">
          <div class="step-number">{{ step.step }}</div>
          <div class="step-line" v-if="index < process.length - 1"></div>
        </div>
        <div class="step-content">
          <h4 class="step-title">{{ step.title }}</h4>
          <p class="step-description">{{ step.description }}</p>
        </div>
      </div> -->
    </div>
  </div>
</template>

<script>
export default {
  name: "ApplicationProcessSection",
  props: {
    process: {
      type: Array,
      required: true
    },
    content: {
      type: String,
      required: true
    }
  }
};
</script>

<style lang="scss" scoped>
.application-process-section {
  background-color: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #323233;
    margin: 0 0 16px 0;
  }

  .process-timeline {
    .process-step {
      display: flex;
      position: relative;

      &:not(.last-step) {
        margin-bottom: 20px;
      }

      .step-indicator {
        position: relative;
        margin-right: 16px;

        .step-number {
          width: 32px;
          height: 32px;
          background: linear-gradient(135deg, #1989fa 0%, #1976d2 100%);
          color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 14px;
          font-weight: 600;
          z-index: 2;
          position: relative;
        }

        .step-line {
          position: absolute;
          top: 32px;
          left: 50%;
          transform: translateX(-50%);
          width: 2px;
          height: 20px;
          background-color: #ebedf0;
        }
      }

      .step-content {
        flex: 1;
        padding-top: 4px;

        .step-title {
          font-size: 15px;
          font-weight: 600;
          color: #323233;
          margin: 0 0 4px 0;
        }

        .step-description {
          font-size: 13px;
          color: #646566;
          line-height: 1.4;
          margin: 0;
        }
      }
    }
  }
}
.content {
  font-size: 14px;
  color: #646566;
}
</style>
