<template>
  <div class="institution-stats-card">
    <div class="stats-content">
      <!-- 机构logo和名称 -->
      <div class="institution-header">
        <div class="institution-logo">
          <img :src="stats.logo" :alt="stats.name" />
        </div>
        <h2 class="institution-name">{{ stats.name }}</h2>
      </div>
      
      <!-- 统计数据 -->
      <div class="stats-data">
        <div class="stat-item">
          <div class="stat-value">{{ stats.productCount }}个</div>
          <div class="stat-label">{{ stats.productLabel }}</div>
        </div>
        <div class="stat-divider"></div>
        <div class="stat-item">
          <div class="stat-value">{{ stats.financingAmount }}</div>
          <div class="stat-label">{{ stats.financingLabel }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'InstitutionStatsCard',
  props: {
    stats: {
      type: Object,
      required: true,
      default: () => ({})
    }
  }
}
</script>

<style lang="scss" scoped>
.institution-stats-card {
  margin: 12px 16px;
  background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 100%);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
  
  .stats-content {
    padding: 20px;
    color: white;
    
    .institution-header {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      
      .institution-logo {
        margin-right: 12px;
        
        img {
          width: 48px;
          height: 48px;
          border-radius: 8px;
          background-color: white;
          padding: 4px;
        }
      }
      
      .institution-name {
        font-size: 18px;
        font-weight: 600;
        margin: 0;
        color: white;
      }
    }
    
    .stats-data {
      display: flex;
      align-items: center;
      justify-content: space-around;
      
      .stat-item {
        text-align: center;
        flex: 1;
        
        .stat-value {
          font-size: 24px;
          font-weight: 700;
          margin-bottom: 4px;
          color: white;
        }
        
        .stat-label {
          font-size: 13px;
          color: rgba(255, 255, 255, 0.9);
        }
      }
      
      .stat-divider {
        width: 1px;
        height: 40px;
        background-color: rgba(255, 255, 255, 0.3);
        margin: 0 20px;
      }
    }
  }
}

// 响应式适配
@media (max-width: 375px) {
  .institution-stats-card {
    margin: 8px 12px;
    
    .stats-content {
      padding: 16px;
      
      .institution-header {
        margin-bottom: 16px;
        
        .institution-logo {
          img {
            width: 40px;
            height: 40px;
          }
        }
        
        .institution-name {
          font-size: 16px;
        }
      }
      
      .stats-data {
        .stat-item {
          .stat-value {
            font-size: 20px;
          }
          
          .stat-label {
            font-size: 12px;
          }
        }
        
        .stat-divider {
          height: 32px;
          margin: 0 16px;
        }
      }
    }
  }
}
</style>
