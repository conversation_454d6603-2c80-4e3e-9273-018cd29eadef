<template>
  <div class="institution-list">
    <!-- 固定头部区域 -->
    <div class="fixed-header">
      <!-- 头部导航 -->
      <van-nav-bar
        title="智能匹配"
        left-arrow
        @click-left="onClickLeft"
        class="custom-nav-bar"
      >
      </van-nav-bar>
      <!-- 搜索栏 -->
      <div class="search-section">
        <div class="search-container">
          <van-search
            v-model="queryParams.name"
            placeholder="请输入公司名称/信用代码搜索"
            shape="round"
            background="transparent"
            @search="resetAndLoad"
            class="search-input"
          />
          <van-button
            type="primary"
            round
            size="small"
            class="post-button"
            @click="resetAndLoad"
          >
            搜索
          </van-button>
        </div>
      </div>
    </div>
    <!-- 滚动内容区域 -->
    <div class="scroll-area">
      <van-list
        :finished="finished"
        :finished-text="filteredInstitutions.length ? '没有更多了' : ''"
      >
        <div class="product-list">
          <ProductCard
            v-for="(product, index) in institutionList"
            :key="product.id || index"
            :product="product"
            @apply="onApplyLoan"
          />
          <van-empty
            v-if="filteredInstitutions.length === 0"
            description="暂无数据"
            image="search"
          />
        </div>
      </van-list>
      <!-- <van-empty
          v-if="filteredInstitutions.length === 0"
          description="暂无相关机构"
          image="search"
        /> -->
    </div>
  </div>
</template>

<script>
import ProductCard from "@/components/ProductCard";
import { getMatchList } from "@/api/smartMatch";

export default {
  name: "InstitutionList",
  components: {
    ProductCard
  },
  data() {
    return {
      finished: false,
      loading: false,
      queryParams: {
        // pageNum: 1,
        // pageSize: 10,
        // total: 0,
        name: ""
        // searchValue: ""
      },
      searchValue: "",
      activeCategory: "bank",
      institutionList: []
    };
  },
  computed: {
    filteredInstitutions() {
      return this.institutionList;
    }
  },
  created() {
    // this.resetAndLoad();
    const name = sessionStorage.getItem("yantai-h5-name");
    if (name) {
      this.queryParams.name = name;
      this.resetAndLoad();
      sessionStorage.removeItem("yantai-h5-name");
    }
  },
  methods: {
    async resetAndLoad() {
      this.page = 1;
      this.finished = false;
      this.institutionList = [];
      await this.getInstitutionList();
    },
    async getInstitutionList() {
      console.log(1);
      if (this.finished || !this.queryParams.name) return;
      // this.loading = true;
      const records = await getMatchList(this.queryParams);
      this.institutionList = records;
      // // this.total = total;
      // this.loading = false;
      // if (this.institutionList.length >= total) {
      //   this.finished = true;
      // }
    },
    onLoadMore() {
      console.log("加载更多");
      if (this.finished) return;
      this.page++;
      this.getInstitutionList();
    },
    onClickLeft() {
      this.$router.go(-1);
    },
    onScan() {
      console.log("扫码功能");
    },
    onSearch(value) {
      console.log("搜索:", value);
      // this.queryParams.data.jpmc = value;
      this.$set(this.queryParams.data, "jpmc", value);
      console.log(this.queryParams);
      this.resetAndLoad();
    },
    onCategoryChange(name, title) {
      this.activeCategory = name;
      this.resetAndLoad();
    },
    onInstitutionClick(institution) {
      this.$router.push({
        name: "InstitutionDetail",
        params: { id: institution.id }
      });
    },
    onApplyLoan(product) {
      sessionStorage.setItem("yantai-h5-name", this.queryParams.name);
      this.$router.push({
        path: "/productDetail",
        query: { product: JSON.stringify(product) }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.institution-list {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f7f8fa;
  overflow: hidden;

  .fixed-header {
    position: sticky;
    top: 0;
    z-index: 10;
    background: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.02);
  }

  .scroll-area {
    flex: 1;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    background: #f7f8fa;
    padding: 12px;
  }

  .custom-nav-bar {
    position: sticky;
    top: 0;
    background-color: white;

    :deep(.van-nav-bar__title) {
      color: #323233;
      font-weight: 500;
    }

    .scan-icon {
      color: #969799;
      font-size: 18px;
    }
  }

  .search-section {
    padding: 12px 16px;
    background-color: white;
    .search-container {
      display: flex;
      align-items: center;
      gap: 12px;
      .search-input {
        flex: 1;
        :deep(.van-search) {
          padding: 0;
          .van-search__content {
            background-color: #f7f8fa;
            border-radius: 20px;
          }
          .van-field__control {
            font-size: 14px;
          }
        }
      }
      .post-button {
        height: 32px;
        padding: 0 16px;
        font-size: 13px;
        white-space: nowrap;
      }
    }
    :deep(.van-search) {
      padding: 0;

      .van-search__content {
        background-color: #f7f8fa;
        border-radius: 20px;
      }

      .van-field__control {
        font-size: 14px;
      }
    }
  }

  .category-tabs {
    background-color: white;
    border-bottom: 1px solid #ebedf0;

    :deep(.van-tabs) {
      .van-tabs__wrap {
        padding: 0 16px;
      }

      .van-tab {
        font-size: 15px;
        color: #646566;

        &.van-tab--active {
          color: #1989fa;
          font-weight: 500;
        }
      }

      .van-tabs__line {
        background-color: #1989fa;
        width: 20px;
        border-radius: 2px;
      }
    }
  }

  .institution-content {
    padding: 8px 16px 16px;

    :deep(.van-empty) {
      padding: 60px 0;
    }
  }
}
</style>
