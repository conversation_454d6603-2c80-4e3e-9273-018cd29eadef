<template>
  <div class="wscn-http403-container">
    <div class="wscn-http403">
      <div class="pic-403">
        <img class="pic-403__parent" src="@/assets/403_images/403.png" alt="403">
        <img class="pic-403__child left" src="@/assets/403_images/403_cloud.png" alt="403">
        <img class="pic-403__child mid" src="@/assets/403_images/403_cloud.png" alt="403">
        <img class="pic-403__child right" src="@/assets/403_images/403_cloud.png" alt="403">
      </div>
      <div class="bullshit">
        <div class="bullshit__oops">OOPS!</div>
        <div class="bullshit__headline">无权访问</div>
        <div class="bullshit__info">请检查你的URL</div>
        <a class="bullshit__return-home" @click="toHome">返回主页</a>
      </div>
    </div>
  </div>
</template>

<script>



export default {
  name: 'Page403',
  computed: {
    message() {
      return 'The webmaster said that you can not enter this page...'
    },
    
  },
  methods: {
    toHome() {
      this.$router.push({ path: '/' })
    }
  }
}
</script>

<style lang="scss" scoped>
.wscn-http403-container{
  height: 100vh;
  width: 100vh;
  overflow: hidden;
}
.wscn-http403 {
  position: relative;
  width: 375px;
  padding: 0 15.625px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 90%;

  .pic-403 {
    position: relative;
    float: left;
    width: 187.5px;
    overflow: hidden;
    &__parent {
      width: 100%;
    }
    &__child {
      position: absolute;
      &.left {
        width: 25px;
        top: 5.3125px;
        left: 68.75px;
        opacity: 0;
        animation-name: cloudLeft;
        animation-duration: 2s;
        animation-timing-function: linear;
        animation-fill-mode: forwards;
        animation-delay: 1s;
      }
      &.mid {
        width: 14.375px;
        top: 0.3125*10px;
        left: 3.125px;
        opacity: 0;
        animation-name: cloudMid;
        animation-duration: 2s;
        animation-timing-function: linear;
        animation-fill-mode: forwards;
        animation-delay: 1.2s;
      }
      &.right {
        width: 19.375px;
        top: 31.25px;
        left: 156.25px;
        opacity: 0;
        animation-name: cloudRight;
        animation-duration: 2s;
        animation-timing-function: linear;
        animation-fill-mode: forwards;
        animation-delay: 1s;
      }
      @keyframes cloudLeft {
        0% {
          top: 5.3125px;
          left: 68.75px;
          opacity: 0;
        }
        20% {
          top: 10.3125px;
          left: 58.75px;
          opacity: 1;
        }
        80% {
          top: 25.3125px;
          left: 28.75px;
          opacity: 1;
        }
        100% {
          top: 30.3125px;
          left: 18.75px;
          opacity: 0;
        }
      }
      @keyframes cloudMid {
        0% {
          top: 3.125px;
          left: 131.25px;
          opacity: 0;
        }
        20% {
          top: 12.5px;
          left: 112.5px;
          opacity: 1;
        }
        70% {
          top: 40.625px;
          left: 56.25px;
          opacity: 1;
        }
        100% {
          top: 50px;
          left: 37.5px;
          opacity: 0;
        }
      }
      @keyframes cloudRight {
        0% {
          top: 31.25*100px;
          left: 156.25px;
          opacity: 0;
        }
        20% {
          top: 37.5px;
          left: 143.75px;
          opacity: 1;
        }
        80% {
          top: 56.25px;
          left: 106.25px;
          opacity: 1;
        }
        100% {
          top: 62.5px;
          left: 93.75px;
          opacity: 0;
        }
      }
    }
  }
  .bullshit {
    position: relative;
    float: left;
    padding: 9.375px 0;
    overflow: hidden;
    &__oops {
      font-size: 24px;
      font-weight: bold;
      color: #1482f0;
      opacity: 0;
      margin-bottom: 8px;
      animation-name: slideUp;
      animation-duration: 0.5s;
      animation-fill-mode: forwards;
    }
    &__headline {
      font-size: 16px;
      color: #222;
      font-weight: bold;
      opacity: 0;
      margin-bottom: 4px;
      animation-name: slideUp;
      animation-duration: 0.5s;
      animation-delay: 0.1s;
      animation-fill-mode: forwards;
    }
    &__info {
      font-size: 12px;
      color: grey;
      opacity: 0;
      margin-bottom: 24px;
      animation-name: slideUp;
      animation-duration: 0.5s;
      animation-delay: 0.2s;
      animation-fill-mode: forwards;
    }
    &__return-home {
      display: block;
      float: left;
      width: 80px;
      height: 30px;
      background: #1482f0;
      border-radius: 100px;
      text-align: center;
      color: #ffffff;
      opacity: 0;
      font-size: 14px;
      line-height: 30px;
      cursor: pointer;
      animation-name: slideUp;
      animation-duration: 0.5s;
      animation-delay: 0.3s;
      animation-fill-mode: forwards;
    }
    @keyframes slideUp {
      0% {
        transform: translateY(60px);
        opacity: 0;
      }
      100% {
        transform: translateY(0);
        opacity: 1;
      }
    }
  }
}
</style>
