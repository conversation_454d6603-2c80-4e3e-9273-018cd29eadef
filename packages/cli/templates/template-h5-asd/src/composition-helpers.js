import { getCurrentInstance } from "vue";

export function useStore() {
  const { proxy } = getCurrentInstance();
  console.log(proxy);
  const store = proxy.$store;
  return store;
}
export function useRoute() {
  const { proxy } = getCurrentInstance();
  const route = proxy.$route;
  console.log(route);
  return route;
}
export function useRouter() {
  const { proxy } = getCurrentInstance();
  const router = proxy.$router;
  return router;
}
